/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: #2c1810;
  background-color: #faf8f5;
  overflow-x: hidden;
}

body.loaded {
  animation: fadeIn 0.8s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-padding {
  padding: 100px 0;
}

/* Typography */
h1,
h2,
h3,
h4 {
  font-family: "Playfair Display", serif;
  font-weight: 600;
  line-height: 1.2;
}

.section-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 20px;
  color: #8b4513;
  position: relative;
  opacity: 0;
  transform: translateY(30px);
  animation: slideInUp 0.8s ease forwards;
}

.section-subtitle {
  text-align: center;
  font-size: 1.1rem;
  color: #6b4423;
  max-width: 600px;
  margin: 0 auto 50px;
  opacity: 0;
  transform: translateY(20px);
  animation: slideInUp 0.8s ease 0.2s forwards;
}

.section-header {
  margin-bottom: 60px;
}

.section-divider {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #8b4513, #cd853f, #d2b48c);
  margin: 0 auto 30px;
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.section-divider::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(139, 69, 19, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  transition: all 0.3s ease;
}

.navbar.scrolled {
  background: rgba(139, 69, 19, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-logo h3 {
  color: #f5deb3;
  font-size: 1.5rem;
  font-weight: 700;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 30px;
}

.nav-link {
  color: #f5deb3;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: #daa520;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: #daa520;
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: #f5deb3;
  margin: 3px 0;
  transition: 0.3s;
}

/* Hero Section */
.hero {
  height: 100vh;
  background: linear-gradient(rgba(139, 69, 19, 0.8), rgba(101, 67, 33, 0.9)),
    url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="leather-texture" patternUnits="userSpaceOnUse" width="30" height="30"><rect width="30" height="30" fill="none"/><circle cx="8" cy="8" r="1.5" fill="rgba(139,69,19,0.3)"/><circle cx="22" cy="22" r="1" fill="rgba(139,69,19,0.2)"/><circle cx="15" cy="25" r="0.8" fill="rgba(139,69,19,0.25)"/><path d="M5,15 Q10,12 15,15 T25,15" stroke="rgba(139,69,19,0.15)" stroke-width="0.5" fill="none"/></pattern></defs><rect width="100" height="100" fill="url(%23leather-texture)"/></svg>');
  opacity: 0.4;
  animation: textureFloat 20s ease-in-out infinite;
}

@keyframes textureFloat {
  0%, 100% { transform: translateX(0) translateY(0); }
  25% { transform: translateX(-10px) translateY(-5px); }
  50% { transform: translateX(5px) translateY(-10px); }
  75% { transform: translateX(-5px) translateY(5px); }
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(139, 69, 19, 0.2) 0%, rgba(101, 67, 33, 0.6) 100%);
  animation: overlayPulse 8s ease-in-out infinite;
}

@keyframes overlayPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.hero-content {
  position: relative;
  z-index: 3;
  max-width: 800px;
  padding: 0 20px;
}

.hero-title {
  font-size: 4rem;
  color: #f5deb3;
  margin-bottom: 20px;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
  font-weight: 700;
  position: relative;
  overflow: hidden;
}

.hero-title::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(245, 222, 179, 0.3), transparent);
  animation: titleShine 3s ease-in-out infinite;
}

@keyframes titleShine {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

.hero-subtitle {
  font-size: 1.5rem;
  color: #daa520;
  margin-bottom: 20px;
  font-weight: 500;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  animation: subtitleGlow 2s ease-in-out infinite alternate;
}

@keyframes subtitleGlow {
  from { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5); }
  to { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5), 0 0 20px rgba(218, 165, 32, 0.3); }
}

.hero-description {
  font-size: 1.1rem;
  color: #f5deb3;
  margin-bottom: 40px;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.cta-button {
  display: inline-block;
  padding: 18px 45px;
  background: linear-gradient(45deg, #8b4513, #a0522d, #cd853f);
  color: #f5deb3;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 2px solid transparent;
  cursor: pointer;
  box-shadow: 0 8px 25px rgba(139, 69, 19, 0.4);
  position: relative;
  overflow: hidden;
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.cta-button:hover::before {
  left: 100%;
}

.cta-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 35px rgba(139, 69, 19, 0.5);
  border-color: #daa520;
  background: linear-gradient(45deg, #a0522d, #cd853f, #daa520);
}

/* Trust Indicators */
.trust-indicators {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-top: 40px;
  flex-wrap: wrap;
}

.trust-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 25px;
  background: rgba(139, 69, 19, 0.2);
  border-radius: 50px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(245, 222, 179, 0.3);
  color: #f5deb3;
  font-weight: 500;
  transition: all 0.3s ease;
}

.trust-item:hover {
  transform: translateY(-5px);
  background: rgba(139, 69, 19, 0.3);
  border-color: rgba(245, 222, 179, 0.5);
  box-shadow: 0 10px 25px rgba(139, 69, 19, 0.3);
}

.trust-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.trust-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: sepia(20%) saturate(1.2);
}

.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
}

.scroll-arrow {
  width: 30px;
  height: 30px;
  border: 2px solid #f5deb3;
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
}

/* Animations */
@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

.fade-in {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease forwards;
}

.fade-in-delay {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease 0.3s forwards;
}

.fade-in-delay-2 {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease 0.6s forwards;
}

.fade-in-delay-3 {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease 0.9s forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* About Section */
.about {
  background: #faf8f5;
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 60px;
  align-items: start;
}

.about-intro {
  font-size: 1.2rem;
  color: #8b4513;
  margin-bottom: 40px;
  font-weight: 500;
  line-height: 1.8;
}

.about-details {
  display: grid;
  gap: 30px;
}

.about-item h3 {
  color: #8b4513;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.about-item p {
  color: #6b4423;
  line-height: 1.7;
}

.team-section {
  background: #f5f2ed;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
}

.team-section h3 {
  color: #8b4513;
  margin-bottom: 30px;
  text-align: center;
  font-size: 1.5rem;
}

.team-grid {
  display: grid;
  gap: 25px;
}

.team-member {
  text-align: center;
  padding: 25px;
  background: white;
  border-radius: 15px;
  transition: all 0.4s ease;
  box-shadow: 0 5px 15px rgba(139, 69, 19, 0.1);
}

.team-member:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(139, 69, 19, 0.2);
}

.team-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 15px;
  border: 3px solid #f5f2ed;
  transition: all 0.3s ease;
}

.team-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.team-member:hover .team-image {
  border-color: #cd853f;
}

.team-member:hover .team-image img {
  transform: scale(1.1);
}

.team-member h4 {
  color: #8b4513;
  margin-bottom: 5px;
  font-size: 1.1rem;
}

.team-member p {
  color: #6b4423;
  font-size: 0.9rem;
}

/* Services Section */
.services {
  background: linear-gradient(135deg, #f5f2ed 0%, #ede7db 100%);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.service-card {
  background: white;
  padding: 0;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 15px 35px rgba(139, 69, 19, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.service-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 20px 20px 0 0;
  position: relative;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.service-card:hover .service-image img {
  transform: scale(1.1);
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #8b4513, #cd853f, #daa520);
  transition: left 0.4s ease;
  z-index: 2;
}

.service-card:hover::before {
  left: 0;
}

.service-card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: 0 25px 50px rgba(139, 69, 19, 0.2);
}



.service-card h3 {
  color: #8b4513;
  margin: 25px 0 20px;
  font-size: 1.4rem;
  padding: 0 30px;
}

.service-card p {
  color: #6b4423;
  line-height: 1.7;
  padding: 0 30px 30px;
}

/* Products Section */
.products {
  background: #faf8f5;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.product-card {
  background: white;
  padding: 0;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 12px 30px rgba(139, 69, 19, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 2px solid #f0ebe0;
  overflow: hidden;
  position: relative;
}

.product-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
  position: relative;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
  filter: sepia(20%) saturate(1.2);
}

.product-card:hover .product-image img {
  transform: scale(1.15);
}

.product-card:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow: 0 20px 45px rgba(139, 69, 19, 0.2);
  border-color: #cd853f;
}



.product-card h3 {
  color: #8b4513;
  margin: 20px 0 15px;
  font-size: 1.3rem;
  padding: 0 20px;
}

.product-card p {
  color: #6b4423;
  margin-bottom: 20px;
  line-height: 1.6;
  padding: 0 20px;
}

.product-uses {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  padding: 0 20px 25px;
}

.product-uses span {
  background: linear-gradient(135deg, #f5f2ed, #ede7db);
  color: #8b4513;
  padding: 6px 14px;
  border-radius: 25px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid #e6d7c3;
  transition: all 0.3s ease;
}

.product-uses span:hover {
  background: linear-gradient(135deg, #cd853f, #daa520);
  color: white;
  transform: translateY(-2px);
}

/* Manufacturing Excellence Section */
.manufacturing {
  background: linear-gradient(135deg, #2c1810 0%, #3d2817 50%, #4a3426 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.manufacturing::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="manufacturing-pattern" patternUnits="userSpaceOnUse" width="40" height="40"><rect width="40" height="40" fill="none"/><circle cx="10" cy="10" r="2" fill="rgba(139,69,19,0.1)"/><circle cx="30" cy="30" r="1.5" fill="rgba(139,69,19,0.08)"/><rect x="15" y="15" width="10" height="10" fill="rgba(139,69,19,0.05)" rx="2"/></pattern></defs><rect width="100" height="100" fill="url(%23manufacturing-pattern)"/></svg>');
  opacity: 0.3;
}

.manufacturing .container {
  position: relative;
  z-index: 2;
}

.manufacturing .section-title {
  color: #f5deb3;
}

.manufacturing .section-subtitle {
  color: #e6d7c3;
}

.manufacturing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 80px;
}

.manufacturing-item {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.4s ease;
}

.manufacturing-image {
  width: 100%;
  height: 250px;
  position: relative;
  overflow: hidden;
}

.manufacturing-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
  filter: sepia(30%) saturate(1.1) brightness(0.9);
}

.manufacturing-item:hover .manufacturing-image img {
  transform: scale(1.1);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(139, 69, 19, 0.9));
  padding: 30px 20px 20px;
  transform: translateY(100%);
  transition: transform 0.4s ease;
}

.manufacturing-item:hover .image-overlay {
  transform: translateY(0);
}

.image-overlay h4 {
  color: #f5deb3;
  font-size: 1.2rem;
  margin: 0;
}

.manufacturing-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.stat-item {
  text-align: center;
  padding: 30px;
  background: rgba(139, 69, 19, 0.2);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(245, 222, 179, 0.2);
  transition: all 0.4s ease;
}

.stat-item:hover {
  transform: translateY(-10px);
  background: rgba(139, 69, 19, 0.3);
  border-color: rgba(245, 222, 179, 0.4);
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  color: #daa520;
  margin-bottom: 10px;
  font-family: "Playfair Display", serif;
}

.stat-label {
  color: #e6d7c3;
  font-size: 1.1rem;
  font-weight: 500;
}

/* Clients Section */
.clients {
  background: linear-gradient(135deg, #f5f2ed 0%, #ede7db 100%);
}

.clients-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
}

.client-types h3,
.export-markets h3 {
  color: #8b4513;
  margin-bottom: 30px;
  font-size: 1.8rem;
  text-align: center;
}

.client-grid,
.markets-grid {
  display: grid;
  gap: 25px;
}

.client-item,
.market-item {
  background: white;
  padding: 0;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
  transition: all 0.4s ease;
  overflow: hidden;
}

.client-item:hover,
.market-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(139, 69, 19, 0.2);
}

.client-image,
.market-image {
  width: 100%;
  height: 150px;
  overflow: hidden;
}

.client-image img,
.market-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
  filter: sepia(15%) saturate(1.1);
}

.client-item:hover .client-image img,
.market-item:hover .market-image img {
  transform: scale(1.1);
}

.client-item h4,
.market-item h4 {
  color: #8b4513;
  margin: 20px 0 10px;
  padding: 0 20px;
}

.client-item p,
.market-item p {
  color: #6b4423;
  font-size: 0.9rem;
  padding: 0 20px 25px;
}

/* Sustainability Section */
.sustainability {
  background: linear-gradient(135deg, #8b4513 0%, #a0522d 50%, #cd853f 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.sustainability::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
  background-size: cover;
  background-position: center;
  opacity: 0.1;
  z-index: 1;
}

.sustainability .container {
  position: relative;
  z-index: 2;
}

.sustainability .section-title {
  color: #f5deb3;
}

.sustainability .section-subtitle {
  color: #e6d7c3;
}

.sustainability-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.sustainability-item {
  background: rgba(139, 69, 19, 0.2);
  padding: 0;
  border-radius: 20px;
  text-align: center;
  backdrop-filter: blur(15px);
  border: 2px solid rgba(245, 222, 179, 0.2);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.sustainability-item::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(245, 222, 179, 0.1) 0%, transparent 70%);
  transform: scale(0);
  transition: transform 0.5s ease;
}

.sustainability-item:hover::before {
  transform: scale(1);
}

.sustainability-item:hover {
  transform: translateY(-12px) scale(1.02);
  background: rgba(139, 69, 19, 0.3);
  border-color: rgba(245, 222, 179, 0.4);
  box-shadow: 0 20px 40px rgba(139, 69, 19, 0.3);
}

.sustainability-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
  border-radius: 20px 20px 0 0;
}

.sustainability-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
  filter: sepia(25%) saturate(1.1) brightness(0.9);
}

.sustainability-item:hover .sustainability-image img {
  transform: scale(1.1);
}

.sustainability-item h3 {
  color: #f5deb3;
  margin: 25px 0 18px;
  font-size: 1.4rem;
  position: relative;
  z-index: 2;
  padding: 0 25px;
}

.sustainability-item p {
  color: #e6d7c3;
  line-height: 1.7;
  position: relative;
  z-index: 2;
  padding: 0 25px 30px;
}

/* Contact Section */
.contact {
  background: #faf8f5;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.contact-info {
  display: grid;
  gap: 30px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 25px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
  transition: all 0.4s ease;
  overflow: hidden;
}

.contact-item:hover {
  transform: translateX(10px);
  box-shadow: 0 15px 35px rgba(139, 69, 19, 0.2);
}

.contact-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  border: 3px solid #f5f2ed;
  transition: all 0.3s ease;
}

.contact-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  filter: sepia(15%) saturate(1.1);
}

.contact-item:hover .contact-image {
  border-color: #cd853f;
}

.contact-item:hover .contact-image img {
  transform: scale(1.1);
}

.contact-item h3 {
  color: #8b4513;
  margin-bottom: 5px;
}

.contact-item p {
  color: #6b4423;
}

.contact-cta {
  text-align: center;
  padding: 40px;
  background: linear-gradient(135deg, #8b4513, #a0522d);
  border-radius: 20px;
  color: white;
}

.contact-cta h3 {
  color: #f5deb3;
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.contact-cta p {
  color: #e6d7c3;
  margin-bottom: 30px;
  line-height: 1.7;
}

/* Footer */
.footer {
  background: #2c1810;
  color: #d2b48c;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h3,
.footer-section h4 {
  color: #f5deb3;
  margin-bottom: 20px;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 10px;
}

.footer-section ul li a {
  color: #d2b48c;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: #daa520;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #4a3426;
  color: #a0522d;
}

/* Enhanced Scroll Animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.animate-on-scroll.animated {
  opacity: 1;
  transform: translateY(0);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #f5f2ed;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #8b4513, #cd853f);
  border-radius: 6px;
  border: 2px solid #f5f2ed;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #a0522d, #daa520);
}

/* Selection styling */
::selection {
  background: rgba(139, 69, 19, 0.3);
  color: #2c1810;
}

/* Focus styles for accessibility */
.nav-link:focus,
.cta-button:focus {
  outline: 2px solid #daa520;
  outline-offset: 2px;
}

/* Loading animation */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #8b4513, #cd853f, #daa520);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Floating animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.floating {
  animation: float 3s ease-in-out infinite;
}

/* Leather texture overlay */
.leather-texture::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><filter id="noise"><feTurbulence baseFrequency="0.9" numOctaves="4" stitchTiles="stitch"/><feColorMatrix values="0 0 0 0 0.545 0 0 0 0 0.271 0 0 0 0 0.075 0 0 0 0.1 0"/></filter></defs><rect width="100" height="100" filter="url(%23noise)" opacity="0.3"/></svg>');
  pointer-events: none;
  mix-blend-mode: multiply;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: rgba(139, 69, 19, 0.98);
    width: 100%;
    text-align: center;
    transition: 0.3s;
    padding: 30px 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu li {
    margin: 15px 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .clients-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .services-grid,
  .products-grid,
  .sustainability-grid {
    grid-template-columns: 1fr;
  }

  .team-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .section-padding {
    padding: 60px 0;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-description {
    font-size: 0.9rem;
  }

  .container {
    padding: 0 15px;
  }
}

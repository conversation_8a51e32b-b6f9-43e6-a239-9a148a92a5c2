/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: #2c1810;
  background-color: #faf8f5;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-padding {
  padding: 100px 0;
}

/* Typography */
h1,
h2,
h3,
h4 {
  font-family: "Playfair Display", serif;
  font-weight: 600;
  line-height: 1.2;
}

.section-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 20px;
  color: #8b4513;
}

.section-subtitle {
  text-align: center;
  font-size: 1.1rem;
  color: #6b4423;
  max-width: 600px;
  margin: 0 auto 50px;
}

.section-header {
  margin-bottom: 60px;
}

.section-divider {
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #8b4513, #d2b48c);
  margin: 0 auto 30px;
  border-radius: 2px;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(139, 69, 19, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  transition: all 0.3s ease;
}

.navbar.scrolled {
  background: rgba(139, 69, 19, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-logo h3 {
  color: #f5deb3;
  font-size: 1.5rem;
  font-weight: 700;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 30px;
}

.nav-link {
  color: #f5deb3;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: #daa520;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: #daa520;
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: #f5deb3;
  margin: 3px 0;
  transition: 0.3s;
}

/* Hero Section */
.hero {
  height: 100vh;
  background: linear-gradient(rgba(139, 69, 19, 0.7), rgba(101, 67, 33, 0.8)),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="leather" patternUnits="userSpaceOnUse" width="20" height="20"><rect width="20" height="20" fill="%23654321"/><circle cx="5" cy="5" r="1" fill="%23543a2a"/><circle cx="15" cy="15" r="1" fill="%23543a2a"/><circle cx="10" cy="18" r="0.5" fill="%23543a2a"/></pattern></defs><rect width="100" height="100" fill="url(%23leather)"/></svg>');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(139, 69, 19, 0.3) 0%, rgba(101, 67, 33, 0.7) 100%);
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  padding: 0 20px;
}

.hero-title {
  font-size: 4rem;
  color: #f5deb3;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  font-weight: 700;
}

.hero-subtitle {
  font-size: 1.5rem;
  color: #daa520;
  margin-bottom: 20px;
  font-weight: 500;
}

.hero-description {
  font-size: 1.1rem;
  color: #f5deb3;
  margin-bottom: 40px;
  opacity: 0.9;
}

.cta-button {
  display: inline-block;
  padding: 15px 40px;
  background: linear-gradient(45deg, #8b4513, #a0522d);
  color: #f5deb3;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(139, 69, 19, 0.4);
  border-color: #daa520;
}

.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
}

.scroll-arrow {
  width: 30px;
  height: 30px;
  border: 2px solid #f5deb3;
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
}

/* Animations */
@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

.fade-in {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease forwards;
}

.fade-in-delay {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease 0.3s forwards;
}

.fade-in-delay-2 {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease 0.6s forwards;
}

.fade-in-delay-3 {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease 0.9s forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* About Section */
.about {
  background: #faf8f5;
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 60px;
  align-items: start;
}

.about-intro {
  font-size: 1.2rem;
  color: #8b4513;
  margin-bottom: 40px;
  font-weight: 500;
  line-height: 1.8;
}

.about-details {
  display: grid;
  gap: 30px;
}

.about-item h3 {
  color: #8b4513;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.about-item p {
  color: #6b4423;
  line-height: 1.7;
}

.team-section {
  background: #f5f2ed;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
}

.team-section h3 {
  color: #8b4513;
  margin-bottom: 30px;
  text-align: center;
  font-size: 1.5rem;
}

.team-grid {
  display: grid;
  gap: 25px;
}

.team-member {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 10px;
  transition: transform 0.3s ease;
}

.team-member:hover {
  transform: translateY(-5px);
}

.team-icon {
  font-size: 2rem;
  margin-bottom: 15px;
}

.team-member h4 {
  color: #8b4513;
  margin-bottom: 5px;
}

.team-member p {
  color: #6b4423;
  font-size: 0.9rem;
}

/* Services Section */
.services {
  background: linear-gradient(135deg, #f5f2ed 0%, #ede7db 100%);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.service-card {
  background: white;
  padding: 40px 30px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(139, 69, 19, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #8b4513, #daa520);
  transition: left 0.3s ease;
}

.service-card:hover::before {
  left: 0;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(139, 69, 19, 0.15);
}

.service-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.service-card h3 {
  color: #8b4513;
  margin-bottom: 20px;
  font-size: 1.4rem;
}

.service-card p {
  color: #6b4423;
  line-height: 1.7;
}

/* Products Section */
.products {
  background: #faf8f5;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.product-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0ebe0;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(139, 69, 19, 0.15);
  border-color: #daa520;
}

.product-icon {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.product-card h3 {
  color: #8b4513;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.product-card p {
  color: #6b4423;
  margin-bottom: 20px;
  line-height: 1.6;
}

.product-uses {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.product-uses span {
  background: #f5f2ed;
  color: #8b4513;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* Clients Section */
.clients {
  background: linear-gradient(135deg, #f5f2ed 0%, #ede7db 100%);
}

.clients-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
}

.client-types h3,
.export-markets h3 {
  color: #8b4513;
  margin-bottom: 30px;
  font-size: 1.8rem;
  text-align: center;
}

.client-grid,
.markets-grid {
  display: grid;
  gap: 25px;
}

.client-item,
.market-item {
  background: white;
  padding: 25px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
  transition: transform 0.3s ease;
}

.client-item:hover,
.market-item:hover {
  transform: translateY(-5px);
}

.client-icon,
.flag {
  font-size: 2rem;
  margin-bottom: 15px;
}

.client-item h4,
.market-item h4 {
  color: #8b4513;
  margin-bottom: 10px;
}

.client-item p,
.market-item p {
  color: #6b4423;
  font-size: 0.9rem;
}

/* Sustainability Section */
.sustainability {
  background: linear-gradient(135deg, #2d5016 0%, #3d6b1f 100%);
  color: white;
}

.sustainability .section-title {
  color: #90ee90;
}

.sustainability .section-subtitle {
  color: #c8e6c9;
}

.sustainability-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.sustainability-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 30px;
  border-radius: 15px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.sustainability-item:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.15);
}

.sustainability-icon {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.sustainability-item h3 {
  color: #90ee90;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.sustainability-item p {
  color: #e8f5e8;
  line-height: 1.6;
}

/* Contact Section */
.contact {
  background: #faf8f5;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.contact-info {
  display: grid;
  gap: 30px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 25px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
  transition: transform 0.3s ease;
}

.contact-item:hover {
  transform: translateX(10px);
}

.contact-icon {
  font-size: 1.5rem;
  width: 50px;
  height: 50px;
  background: #f5f2ed;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-item h3 {
  color: #8b4513;
  margin-bottom: 5px;
}

.contact-item p {
  color: #6b4423;
}

.contact-cta {
  text-align: center;
  padding: 40px;
  background: linear-gradient(135deg, #8b4513, #a0522d);
  border-radius: 20px;
  color: white;
}

.contact-cta h3 {
  color: #f5deb3;
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.contact-cta p {
  color: #e6d7c3;
  margin-bottom: 30px;
  line-height: 1.7;
}

/* Footer */
.footer {
  background: #2c1810;
  color: #d2b48c;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h3,
.footer-section h4 {
  color: #f5deb3;
  margin-bottom: 20px;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 10px;
}

.footer-section ul li a {
  color: #d2b48c;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: #daa520;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #4a3426;
  color: #a0522d;
}

/* Scroll Animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease;
}

.animate-on-scroll.animated {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: rgba(139, 69, 19, 0.98);
    width: 100%;
    text-align: center;
    transition: 0.3s;
    padding: 30px 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu li {
    margin: 15px 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .clients-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .services-grid,
  .products-grid,
  .sustainability-grid {
    grid-template-columns: 1fr;
  }

  .team-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .section-padding {
    padding: 60px 0;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-description {
    font-size: 0.9rem;
  }

  .container {
    padding: 0 15px;
  }
}
